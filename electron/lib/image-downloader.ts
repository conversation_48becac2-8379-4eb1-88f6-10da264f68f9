import * as fs from 'fs'
import * as path from 'path'
import { makeHttpRequest } from './http-client'

export interface DownloadImagesParams {
  imageUrls: {[nodeId: string]: string}
  downloadPath: string
}

export interface DownloadResult {
  success: boolean
  data?: {
    filePaths: string[]
  }
  error?: string
}

/**
 * 下载图片到指定路径
 * @param imageUrls 图片URL映射对象，格式如 {"64:192": "https://..."}
 * @param downloadPath 下载路径
 * @returns Promise<DownloadResult>
 */
export async function downloadImages({ imageUrls, downloadPath }: DownloadImagesParams): Promise<DownloadResult> {
  try {
    // 确保下载目录存在
    if (!fs.existsSync(downloadPath)) {
      fs.mkdirSync(downloadPath, { recursive: true })
    }

    const filePaths: string[] = []
    const downloadPromises: Promise<void>[] = []

    // 为每个图片URL创建下载任务
    for (const [nodeId, imageUrl] of Object.entries(imageUrls)) {
      if (!imageUrl) continue

      const downloadPromise = downloadSingleImage(nodeId, imageUrl, downloadPath)
        .then(filePath => {
          if (filePath) {
            filePaths.push(filePath)
          }
        })
        .catch(error => {
          console.error(`下载图片 ${nodeId} 失败:`, error)
          // 不抛出错误，继续下载其他图片
        })

      downloadPromises.push(downloadPromise)
    }

    // 等待所有下载完成
    await Promise.all(downloadPromises)

    return {
      success: true,
      data: {
        filePaths
      }
    }

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * 下载单个图片
 * @param nodeId 节点ID
 * @param imageUrl 图片URL
 * @param downloadPath 下载路径
 * @returns Promise<string | null> 返回文件路径或null（如果失败）
 */
async function downloadSingleImage(nodeId: string, imageUrl: string, downloadPath: string): Promise<string | null> {
  try {
    // 获取图片数据
    const response = await makeHttpRequest(imageUrl, {
      responseType: 'buffer'
    })

    // 生成文件名：保持原始格式，但处理文件系统不支持的字符
    const fileName = `${nodeId.replace(/[<>:"/\\|?*]/g, '_')}.png`
    const filePath = path.join(downloadPath, fileName)

    // 写入文件
    fs.writeFileSync(filePath, response.data)

    console.log(`图片下载成功: ${fileName}`)
    return filePath

  } catch (error) {
    console.error(`下载图片 ${nodeId} 失败:`, error)
    return null
  }
}
