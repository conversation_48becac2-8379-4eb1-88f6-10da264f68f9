/**
 * Figma API 封装 (Electron版本)
 * 通过IPC与主进程通信来处理HTTP请求
 */

// 导入统一的类型定义
import type {
  FigmaNodesResponse,
  FigmaLinkInfo,
  FigmaImagesResponse
} from '../../type';
import * as yaml from 'js-yaml';

/**
 * 解析 Figma 分享链接，提取文件 key 和节点 ID
 * 通过IPC调用主进程中的解析函数
 */
export async function parseFigmaUrl(url: string): Promise<FigmaLinkInfo> {
  try {
    const result = await window.ipcRenderer.invoke('parse-figma-url', url);

    if (!result.success) {
      throw new Error(result.error);
    }

    return result.data;
  } catch (error) {
    throw new Error(`解析 Figma 链接失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 获取 Figma 文件中指定节点的信息
 * @param fileKey Figma 文件的 key
 * @param nodeIds 节点 ID 数组，格式如 ['46-6', '47-8']
 * @returns Promise<FigmaNodesResponse>
 */
export async function getFigmaNodes(fileKey: string, nodeIds: string[]): Promise<FigmaNodesResponse> {
  const url = `https://api.figma.com/v1/files/${fileKey}/nodes`;

  try {
    const result = await window.ipcRenderer.invoke('figma-api-request', {
      url,
      params: {
        ids: nodeIds.join(',')
      }
    });

    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.err) {
      throw new Error(`Figma API 错误: ${result.data.err}`);
    }

    return result.data;
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`获取 Figma 节点信息失败: ${error.message}`);
    }
    throw new Error('获取 Figma 节点信息失败: 未知错误');
  }
}

/**
 * 根据 Figma 分享链接获取文件信息
 * @param figmaUrl Figma 分享链接
 * @returns Promise<FigmaNodesResponse>
 */
export async function getFigmaInfoFromUrl(figmaUrl: string): Promise<FigmaNodesResponse> {
  // 解析链接
  const linkInfo = await parseFigmaUrl(figmaUrl);

  // 如果有节点 ID，获取指定节点信息
  if (linkInfo.nodeId) {
    return await getFigmaNodes(linkInfo.fileKey, [linkInfo.nodeId]);
  }

  // 如果没有节点 ID，获取文件基本信息
  try {
    const fileInfoUrl = `https://api.figma.com/v1/files/${linkInfo.fileKey}`;
    const result = await window.ipcRenderer.invoke('figma-api-request', {
      url: fileInfoUrl
    });

    if (!result.success) {
      throw new Error(result.error);
    }

    // 返回文件基本信息
    return {
      name: result.data.name || 'Unknown File',
      role: 'viewer',
      lastModified: result.data.lastModified || '',
      editorType: 'figma',
      thumbnailUrl: result.data.thumbnailUrl || '',
      version: result.data.version || '0',
      linkAccess: 'view',
      nodes: {}
    };
  } catch (error) {
    throw new Error(`获取 Figma 文件信息失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 获取 Figma 文件中的所有图片
 * @param fileKey Figma 文件的 key
 * @returns Promise<FigmaImagesResponse>
 */
export async function getFigmaImages(fileKey: string): Promise<FigmaImagesResponse> {
  const url = `https://api.figma.com/v1/files/${fileKey}/images`;

  try {
    const result = await window.ipcRenderer.invoke('figma-api-request', {
      url
    });

    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.error) {
      throw new Error(`Figma API 错误: ${result.data.error}`);
    }

    return result.data;
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`获取 Figma 图片信息失败: ${error.message}`);
    }
    throw new Error('获取 Figma 图片信息失败: 未知错误');
  }
}

/**
 * 根据 Figma 分享链接获取文件中的所有图片
 * @param figmaUrl Figma 分享链接
 * @returns Promise<FigmaImagesResponse>
 */
export async function getFigmaImagesFromUrl(figmaUrl: string): Promise<FigmaImagesResponse> {
  // 解析链接获取文件 key
  const linkInfo = await parseFigmaUrl(figmaUrl);

  // 获取图片信息
  return await getFigmaImages(linkInfo.fileKey);
}

/**
 * 打印 Figma 文件信息到控制台
 * @param figmaUrl Figma 分享链接
 */
export async function printFigmaInfo(figmaUrl: string): Promise<void> {
  try {
    console.log('🔍 正在解析 Figma 链接...');
    const linkInfo = await parseFigmaUrl(figmaUrl);

    console.log('📋 链接解析结果:');
    console.log(JSON.stringify(linkInfo, null, 2));
    console.log('');

    console.log('📡 正在获取 Figma 文件信息...');
    const figmaInfo = await getFigmaInfoFromUrl(figmaUrl);

    console.log('✅ 获取成功！完整文件信息 JSON:');
    console.log(JSON.stringify(figmaInfo, null, 2));

    console.log('');
    console.log('🎉 Figma 文件信息获取完成！');

  } catch (error) {
    console.error('❌ 获取 Figma 文件信息失败:');
    console.error(`   ${error instanceof Error ? error.message : '未知错误'}`);
    throw error;
  }
}

/**
 * 递归删除节点中的指定字段
 * @param node 节点对象
 * @param fieldsToRemove 要删除的字段数组
 * @returns 清理后的节点对象
 */
function removeFieldsRecursively(node: any, fieldsToRemove: string[]): any {
  if (!node || typeof node !== 'object') {
    return node
  }

  // 创建节点的浅拷贝
  const cleanedNode = { ...node }

  // 删除通用字段
  fieldsToRemove.forEach(field => {
    delete cleanedNode[field]
  })

  // 如果节点类型是VECTOR，删除额外的字段
  if (cleanedNode.type === 'VECTOR') {
    const vectorFieldsToRemove = [
      'blendMode',
      'fills',
      'strokes',
      'strokeWeight',
      'strokeAlign'
    ]

    vectorFieldsToRemove.forEach(field => {
      delete cleanedNode[field]
    })
  }

  // 如果有children，递归处理每个子节点
  if (cleanedNode.children && Array.isArray(cleanedNode.children)) {
    cleanedNode.children = cleanedNode.children.map((child: any) =>
      removeFieldsRecursively(child, fieldsToRemove)
    )
  }

  return cleanedNode
}

/**
 * 处理 Figma API 响应数据，将包含多个子元素的document节点拆分成多个只包含单个子元素的document节点
 * 同时递归删除指定的字段，并转换为紧凑的YAML Flow Style格式以最大化压缩效果
 *
 * 优化策略：
 * 1. 删除所有节点的无用字段：constraints, absoluteRenderBounds, effects, interactions, scrollBehavior
 * 2. 删除VECTOR节点的额外字段：blendMode, fills, strokes, strokeWeight, strokeAlign
 * 3. 使用YAML Flow Style格式，预计压缩率45-55%
 *
 * @param figmaResponse Figma API 响应数据
 * @returns 处理后的YAML字符串数组，每个元素只包含document结构和一个子元素
 */
export function processFigmaData(figmaResponse: any): string[] {
  const result: string[] = []

  // 要删除的字段列表
  const fieldsToRemove = ['constraints', 'absoluteRenderBounds', 'effects', 'interactions', 'scrollBehavior']

  // 检查响应数据结构
  if (!figmaResponse || !figmaResponse.nodes) {
    return result
  }

  // 遍历所有节点
  Object.keys(figmaResponse.nodes).forEach(nodeId => {
    const nodeInfo = figmaResponse.nodes[nodeId]

    // 检查是否有document和children
    if (nodeInfo && nodeInfo.document && nodeInfo.document.children && Array.isArray(nodeInfo.document.children)) {
      const document = nodeInfo.document
      const children = document.children

      // 为每个子元素创建一个新的document结构，只包含document部分
      children.forEach((child: any) => {
        // 先清理子节点中的指定字段
        const cleanedChild = removeFieldsRecursively(child, fieldsToRemove)

        // 清理document本身的指定字段
        const cleanedDocument = removeFieldsRecursively(document, fieldsToRemove)

        const newDocumentStructure = {
          document: {
            ...cleanedDocument, // 保持原有的document属性（已清理）
            children: [cleanedChild] // 只包含当前子元素（已清理）
          }
        }

        // 转换为YAML格式并添加到结果数组 (使用Flow Style获得最佳压缩)
        const yamlString = yaml.dump(newDocumentStructure, {
          flowLevel: 0,     // 所有层级都使用flow style (最紧凑)
          lineWidth: -1,    // 不限制行宽
          noRefs: true,     // 不使用引用
          skipInvalid: true, // 跳过无效值
          quotingType: '"', // 使用双引号
          forceQuotes: false // 不强制所有字符串都加引号
        })
        result.push(yamlString)
      })
    } else {
      // 如果没有children或children不是数组，只返回document结构
      if (nodeInfo && nodeInfo.document) {
        // 清理document中的指定字段
        const cleanedDocument = removeFieldsRecursively(nodeInfo.document, fieldsToRemove)

        const documentStructure = {
          document: cleanedDocument
        }

        // 转换为YAML格式并添加到结果数组 (使用Flow Style获得最佳压缩)
        const yamlString = yaml.dump(documentStructure, {
          flowLevel: 0,     // 所有层级都使用flow style (最紧凑)
          lineWidth: -1,    // 不限制行宽
          noRefs: true,     // 不使用引用
          skipInvalid: true, // 跳过无效值
          quotingType: '"', // 使用双引号
          forceQuotes: false // 不强制所有字符串都加引号
        })
        result.push(yamlString)
      }
    }
  })

  return result
}

/**
 * 递归遍历节点，查找所有图片节点的ID
 * @param node 要遍历的节点
 * @param imageNodeIds 用于收集图片节点ID的数组
 */
function collectImageNodeIds(node: any, imageNodeIds: string[]): void {
  if (!node || typeof node !== 'object') {
    return;
  }

  // 判断当前节点是否是图片节点
  const isImageNode =
    // 方式1: 节点类型是IMAGE
    node.type === 'IMAGE' ||
    // 方式2: 节点的fills属性数组中有type为"IMAGE"的项
    (node.fills && Array.isArray(node.fills) &&
     node.fills.some((fill: any) => fill && fill.type === 'IMAGE'));

  // 如果是图片节点，添加到结果数组
  if (isImageNode && node.id) {
    imageNodeIds.push(node.id);
  }

  // 递归遍历子节点
  if (node.children && Array.isArray(node.children)) {
    node.children.forEach((child: any) => {
      collectImageNodeIds(child, imageNodeIds);
    });
  }
}

/**
 * 获取Figma链接中所有图片节点的ID
 * @param figmaUrl Figma分享链接
 * @returns Promise<string> 返回用逗号分隔的图片节点ID字符串
 */
export async function getImageNodeIdsFromUrl(figmaUrl: string): Promise<string> {
  try {
    // 解析链接获取文件key
    const linkInfo = await parseFigmaUrl(figmaUrl);

    // 获取完整的文件信息（包含所有节点）
    const fileInfoUrl = `https://api.figma.com/v1/files/${linkInfo.fileKey}`;
    const result = await window.ipcRenderer.invoke('figma-api-request', {
      url: fileInfoUrl
    });

    if (!result.success) {
      throw new Error(result.error);
    }

    const fileData = result.data;
    const imageNodeIds: string[] = [];

    // 遍历文件中的所有页面和节点
    if (fileData.document && fileData.document.children) {
      fileData.document.children.forEach((page: any) => {
        collectImageNodeIds(page, imageNodeIds);
      });
    }

    // 返回用逗号分隔的ID字符串
    return imageNodeIds.join(',');

  } catch (error) {
    throw new Error(`获取图片节点ID失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 根据节点ID列表获取对应的图片地址
 * @param fileKey Figma文件的key
 * @param nodeIds 节点ID数组，格式如 ['64:192', '12:21']
 * @param scale 图片缩放比例，默认为2
 * @param useAbsoluteBounds 是否使用绝对边界，默认为true
 * @returns Promise<{[nodeId: string]: string}> 返回节点ID到图片URL的映射对象
 */
export async function getImageUrlsByNodeIds(
  fileKey: string,
  nodeIds: string[],
  scale: number = 2,
  useAbsoluteBounds: boolean = true
): Promise<{[nodeId: string]: string}> {
  if (!nodeIds || nodeIds.length === 0) {
    return {};
  }

  const url = `https://api.figma.com/v1/images/${fileKey}`;

  try {
    const result = await window.ipcRenderer.invoke('figma-api-request', {
      url,
      params: {
        ids: nodeIds.join(','),
        scale: scale.toString(),
        use_absolute_bounds: useAbsoluteBounds.toString()
      }
    });

    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.err) {
      throw new Error(`Figma API 错误: ${result.data.err}`);
    }

    // 返回图片URL映射对象
    return result.data.images || {};

  } catch (error) {
    throw new Error(`获取图片URL失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 根据Figma链接和节点ID字符串获取对应的图片地址
 * @param figmaUrl Figma分享链接
 * @param nodeIds 节点ID字符串，格式如 'id1,id2,id3'
 * @param scale 图片缩放比例，默认为2
 * @param useAbsoluteBounds 是否使用绝对边界，默认为true
 * @returns Promise<{[nodeId: string]: string}> 返回节点ID到图片URL的映射对象
 */
export async function getImageUrlsFromUrl(
  figmaUrl: string,
  nodeIds: string,
  scale: number = 2,
  useAbsoluteBounds: boolean = true
): Promise<{[nodeId: string]: string}> {
  try {
    // 解析链接获取文件key
    const linkInfo = await parseFigmaUrl(figmaUrl);

    // 将逗号分隔的字符串转换为数组
    const nodeIdArray = nodeIds.split(',').filter(id => id.trim().length > 0);

    // 调用基础方法获取图片URL
    return await getImageUrlsByNodeIds(linkInfo.fileKey, nodeIdArray, scale, useAbsoluteBounds);

  } catch (error) {
    throw new Error(`从URL获取图片地址失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 下载图片到指定路径
 * @param imageUrls 图片URL映射对象，格式如 {"64:192": "https://..."}
 * @param downloadPath 下载路径
 * @returns Promise<string[]> 返回下载成功的文件路径数组
 */
export async function downloadImages(
  imageUrls: {[nodeId: string]: string},
  downloadPath: string
): Promise<string[]> {
  try {
    const result = await window.ipcRenderer.invoke('download-images', {
      imageUrls,
      downloadPath
    });

    if (!result.success) {
      throw new Error(result.error);
    }

    return result.data.filePaths || [];

  } catch (error) {
    throw new Error(`下载图片失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

// 导出所有功能
export default {
  parseFigmaUrl,
  getFigmaNodes,
  getFigmaInfoFromUrl,
  getFigmaImages,
  getFigmaImagesFromUrl,
  printFigmaInfo,
  processFigmaData,
  getImageNodeIdsFromUrl,
  getImageUrlsByNodeIds,
  getImageUrlsFromUrl,
  downloadImages
};
